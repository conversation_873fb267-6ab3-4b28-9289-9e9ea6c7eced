<script setup lang="ts">
import KlgInfo from './KlgInfo.vue';
import PreKlgEditList from '@/components/PreKlgEditList.vue';
import ModeTypeSwitcher from '@/components/ModeTypeSwitcher.vue';
import NoteDrawer from '@/components/NoteDrawer.vue';
import ComfirmDialog from './ComfirmDialog.vue';
import ShowAnswerDrawer from '@/components/ShowAnswerDrawer.vue';
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { getKlgDetailApi, getProofBlockListApi, getKlgRefListApi } from '@/apis/path/klg';
import type { AreaListItem, ProofListItem, RefListItem } from '@/utils/type';
import { findKeyByValue } from '@/utils/func';
import { KlgTypeDict, klgAuditType, klgAuditTypeDict, KlgType } from '@/utils/constant';
import { userInfoStore } from '@/stores/userInfo';
import { processAllLatexEquations } from '@/utils/latexUtils';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
const route = useRoute();
const curKlgCode = ref('');
const klgInfoRef = ref();
const quesListRef = ref();
const drawerRef = ref();

// 添加模式切换状态，0: 知识内容，1: 问题列表
const currentMode = ref(0);

// 用户信息
const userStore = userInfoStore();

// 知识详情数据 - 扩展为完整的数据结构
const dataForm = ref({
  klgCode: '',
  title: '',
  type: -1,
  author: '',
  saveTime: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});
const curStatus = ref();

// 处理模式切换
const handleModeChange = (mode: number) => {
  const previousMode = currentMode.value;
  currentMode.value = mode;

  // 管理组件的激活状态
  nextTick(() => {
    // 当切换到知识内容模式时，停用问题列表组件
    if (mode === 0 && quesListRef.value && typeof quesListRef.value.setComponentActive === 'function') {
      quesListRef.value.setComponentActive(false);
    }

    // 当切换到问题列表模式时，激活问题列表组件并重新初始化
    if (mode === 1 && quesListRef.value) {
      if (typeof quesListRef.value.setComponentActive === 'function') {
        quesListRef.value.setComponentActive(true);
      }
      // 如果是从其他模式切换过来，重新初始化数据
      if (previousMode !== 1 && typeof quesListRef.value.initData === 'function') {
        quesListRef.value.initData();
      }
    }
  });
};

// 获取证明过程列表
const getProofList = async (): Promise<Boolean> => {
  if (curKlgCode.value) {
    const res = await getProofBlockListApi(curKlgCode.value);
    if (res.success) {
      dataForm.value.proofList = res.data.klgProofBlocks;
      console.log('MYproofList', dataForm.value.proofList);

      return true;
    }
  }
  return false;
};

// 获取审核信息
const getAuditInfo = async () => {
  if (curKlgCode.value) {
    const res = await getKlgRefListApi(curKlgCode.value);
    if (res.success) {
      dataForm.value.refList = res.data.klgToRefVoList;
    }
  }
};

// 获取klg详情 - 扩展为获取完整数据
const getKlgDetail = async () => {
  try {
    if (curKlgCode.value) {
      const res = await getKlgDetailApi(curKlgCode.value);
      if (res.success) {
        const newStatus = res.data.detail.status;

        // 只有当状态真正发生变化时才触发事件
        // 并且只在知识内容模式下触发，避免影响问题列表模式
        if (curStatus.value !== newStatus && currentMode.value === 0) {
          curStatus.value = newStatus;
          // 延迟触发事件，确保当前组件状态更新完成
          nextTick(() => {
            emitter.emit(Event.SET_STATUS, newStatus);
          });
        } else {
          curStatus.value = newStatus;
        }

        dataForm.value.klgCode = res.data.detail.klgCode;
        dataForm.value.title = res.data.detail.title;
        dataForm.value.type = res.data.detail.sortId;
        dataForm.value.cnt = res.data.detail.cnt;
        dataForm.value.note = res.data.detail.notice;
        dataForm.value.author = res.data.detail.creatorName;
        dataForm.value.saveTime = res.data.detail.modifiedTime;

        if (res.data.detail.sysTitles !== '') {
          dataForm.value.synList = res.data.detail.sysTitles.split('@@');
        }
        dataForm.value.areaList = res.data.areaList.map((item: any) => {
          return {
            areaCode: item.areaCode,
            label: item.title
          };
        });

        // 如果是原理类型，获取证明过程
        if (dataForm.value.type === KlgType.Principles) {
          await getProofList();
          console.log('getProofList', dataForm.value.proofList);
        }

        // 获取审核信息
        await getAuditInfo();
      }
    }
  } catch (error) {
    console.log(error);
  }
};

// 处理打开drawer
const handleDrawer = () => {
  drawerRef.value.showDrawer(dataForm.value.note);
};

// 处理重新编辑
const handleReEdit = () => {
  window.open(`/editklg?klgCode=${dataForm.value.klgCode}`, '_self');
};

// 处理 Event.SHOW_DRAWER 事件，用于在问题列表模式下显示答案抽屉
const handleShowDrawer = (data: any) => {
  // 触发 showAnswerFromFloating 自定义事件，ShowAnswerDrawer 会监听这个事件
  const showAnswerEvent = new CustomEvent('showAnswerFromFloating', {
    detail: { question: data }
  });
  window.dispatchEvent(showAnswerEvent);
};

// 生命周期钩子
onMounted(() => {
  // 监听 Event.SHOW_DRAWER 事件，确保在问题列表模式下也能显示答案抽屉
  emitter.on(Event.SHOW_DRAWER, handleShowDrawer);
});

onUnmounted(() => {
  // 清理事件监听
  emitter.off(Event.SHOW_DRAWER, handleShowDrawer);
});

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case klgAuditType.draft:
      return 'status-draft';
    case klgAuditType.pending:
      return 'status-pending';
    case klgAuditType.reviewing:
      return 'status-reviewing';
    case klgAuditType.returned:
      return 'status-returned';
    case klgAuditType.published:
      return 'status-published';
    case klgAuditType.withdrawn:
      return 'status-withdrawn';
    case klgAuditType.deleted:
      return 'status-deleted';
    default:
      return 'status-unknown';
  }
};

watch(
  () => route.query.klgCode,
  () => {
    if (route.query.klgCode) {
      curKlgCode.value = route.query.klgCode.toString();
      getKlgDetail();
    }
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <!-- 知识基本信息 -->
    <div class="klg-header">
      <div class="header">
        <span class="header-left">
          <span class="header-title"
            ><span class="ck-content" v-html="processAllLatexEquations(dataForm.title)"></span
          ></span>
          <span class="header-type">{{ findKeyByValue(dataForm.type, KlgTypeDict) }}</span>
        </span>
        <span class="header-right" @click="handleDrawer">
          <img src="@/assets/image/klg/u1695.svg" />
          <span class="detail-info-right-text">查看编者笔记</span>
        </span>
      </div>
      <div class="base-info" style="margin: 5px 0">
        <span class="author-info">
          <span style="margin-right: 10px">作者</span>
          <span>{{ dataForm.author }}</span>
        </span>
        <span class="save-time">
          <span style="margin-right: 10px">保存时间</span>
          <span>{{ dataForm.saveTime }}</span>
        </span>
      </div>
      <div class="detail-info">
        <span class="detail-info-left">
          <span class="info-syn"
            ><span class="sameMean">同义词</span>
            <div
              v-if="dataForm.synList.length === 0"
              style="white-space: nowrap; color: var(--color-grey)"
            >
              暂无同义词
            </div>
            <div v-else class="info-syn-text-block">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.synList"
                :key="index"
              >
                <span class="ck-content" v-html="item"> </span>
              </el-tag>
            </div>
          </span>
          <span class="info-syn">
            <span style="white-space: nowrap; margin-right: 60px">所属领域</span>
            <div class="info-syn-text-block" style="margin-top: 5px">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.areaList"
                :key="index"
              >
                <span class="ck-content" v-html="item.label"> </span>
              </el-tag>
            </div>
          </span>
        </span>
        <span class="detail-info-right" :class="getStatusClass(curStatus)">
          {{ findKeyByValue(curStatus, klgAuditTypeDict) || '未知状态' }}
        </span>
      </div>
      <div style="margin-top: 5px" class="line"></div>
    </div>

    <!-- 模式切换器 -->
    <div class="switcher-container">
      <ModeTypeSwitcher
        :mode="currentMode"
        :length="2"
        :modeValues="[0, 1]"
        @changeMode="handleModeChange"
      >
        <template #mode0>知识内容</template>
        <template #mode1>问题列表</template>
      </ModeTypeSwitcher>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 知识内容模式 -->
      <div v-if="currentMode === 0" class="content-panel">
        <KlgInfo
          ref="klgInfoRef"
          :editable="true"
          :klgCode="curKlgCode"
          :dataForm="dataForm"
          :status="curStatus"
        ></KlgInfo>
      </div>

      <!-- 问题列表模式 -->
      <div v-if="currentMode === 1" class="content-panel">
        <PreKlgEditList ref="quesListRef"></PreKlgEditList>
      </div>
    </div>
  </div>
  <!-- other -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
  <!-- ShowAnswerDrawer 全局组件，供两种模式共用 -->
  <ShowAnswerDrawer />
  <!-- 全局的问题编辑对话框，供两种模式共用 -->
  <ComfirmDialog></ComfirmDialog>
</template>
<style scoped lang="less">
.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 1200px;
  min-height: 100vh;
  background-color: #ffffff;
  border-radius: 2px;
  padding: 30px;
}

.klg-header {
  width: 100%;
  max-width: 1200px;
  margin-bottom: 10px;
}

.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 10px;
}

.header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  margin-right: 10px;
}

.header-title :deep(p) {
  margin: 0;
}

.header-type {
  white-space: nowrap;
  display: flex;
  height: 20px;
  padding: 0 5px;
  border: 1px solid var(--color-grey);
  color: var(--color-grey);
  border-radius: 3px;
  font-size: 14px;
  min-width: 80px;
  margin-right: 10px;
  align-items: center;
  justify-content: center;
}

.header-right {
  min-width: 70px;
  cursor: pointer;
  display: flex;
  align-items: center;
  cursor: pointer;
  .detail-info-right-text {
    color: var(--color-primary);
    margin-left: 5px;
    font-size: 12px;
  }
}

.header-right-text {
  white-space: nowrap;
  margin-left: 5px;
  color: var(--color-primary);
  font-size: 12px;
}
.base-info {
  width: 100%;
  color: var(--color-deep);
  font-size: 12px;
  margin: 5px 0;
}

.author-info {
  margin-right: 40px;
}

.save-time {
  display: inline-block;
}

.detail-info {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color: var(--color-black);
  font-size: 14px;
  position: relative;
}

.detail-info-left {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.info-syn {
  width: 90%;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  flex-direction: row;
  .sameMean {
    white-space: nowrap;
    margin-right: 75px;
  }
}

.info-syn-text-block {
  width: 100%;
  white-space: normal;
  overflow-wrap: break-word;
}

.info-syn-text {
  font-weight: 400;
  white-space: normal;
}

.detail-info-right {
  width: 82px;
  height: 20px;
  background-color: #d6e9f6;
  border-radius: 4px;
  position: absolute;
  bottom: 2px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.detail-info-right-text {
  color: var(--color-primary);
  margin-left: 5px;
  font-size: 12px;
}

.switcher-container {
  width: 100%;
  max-width: 1200px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}
.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.content-panel {
  width: 100%;
  border-radius: 8px;
  min-height: 500px;
}

.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}

/* 状态样式 */

.status-draft {
  /* color: #909399; 灰色 - 草稿 */
  color: var(--color-primary);
}

.status-pending {
  color: #e6a23c; /* 橙色 - 待审核 */
}

.status-reviewing {
  color: #409eff; /* 蓝色 - 审核中 */
}

.status-returned {
  color: #f56c6c; /* 红色 - 已退回 */
}

.status-published {
  color: #67c23a; /* 绿色 - 已发布 */
}

.status-withdrawn {
  color: #c45656; /* 深红色 - 已撤回 */
}

.status-deleted {
  color: #f56c6c; /* 红色 - 已删除待恢复 */
}
</style>
